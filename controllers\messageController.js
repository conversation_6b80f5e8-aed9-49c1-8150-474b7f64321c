import Message from "../models/messageModel.js";
import Conversation from "../models/conversationModel.js";
import Pusher from "pusher";
import User from "../models/userModel.js";
import {resetTicketTimer} from "../utils/ticketUtils.js";

const pusher = new Pusher({
  appId: process.env.PUSHER_ID ?? "*******",
  key: process.env.PUSHER_KEY ?? "09b1adba58d8659c01d4",
  secret: process.env.PUSHER_SECRET ?? "d7b085a06ebed7262350",
  cluster: "ap2",
  useTLS: true,
});

export const sendMessage = async (req, res) => {
  try {
    const { conversationId, content, attachments, ticketId } = req.body;
    const senderId = req.user.id;

    // Validate conversation exists and user is participant
    const conversation = await Conversation.findById(conversationId);
    if (!conversation)
      return res.status(404).json({ error: "Conversation not found" });
    if (
      ![
        conversation.client.toString(),
        conversation.seller.toString(),
      ].includes(senderId)
    ) {
      return res.status(403).json({ error: "Unauthorized" });
    }

    // Create message
    const message = new Message({
      conversation: conversationId,
      sender: senderId,
      content,
      attachments,
    });

    await message.save();

    // Update conversation
    conversation.lastMessage = content;
    if (senderId === conversation.client.toString()) {
      conversation.unreadSeller += 1;
    } else {
      conversation.unreadClient += 1;
    }
    await conversation.save();

    // Trigger Pusher event
    pusher.trigger(`conversation-${conversationId}`, "new-message", {
      ...message.toObject(),
      sender: {
        _id: senderId,
        name: req.user.name,
        avatar: req.user.avatar,
        role: req.user.role,
      },
    });

    const receiverId =
        senderId === conversation.client.toString()
            ? conversation.seller.toString()
            : conversation.client.toString();

    const user = await User.findById(senderId);

    const now = new Date();
    const createdAt = now.toISOString();

    const fullNotification = {
      _id:`${Math.random()}`, // this id is to make sure data is clean, its temp id
      userId: receiverId, // this id fetches notifications for a specific user,
      senderId,
      isRead: false,
      // title: `New message from ${user.name}`,
     title: `Ticket responded by ${user.name}`,
      message: content,
      entityType : ticketId ? 'conversation-ticket' : 'conversation-offer',
      entityId: ticketId ?? conversationId,
      entity: {
        _id: conversationId,
        templateCoverImage: user.avatar
      },
      createdAt,
      _v: 0
    }

    pusher.trigger(`private-user-${receiverId}`, 'new-notification', fullNotification);

    ticketId && User.findById(senderId).then(user => user.role === "admin" && resetTicketTimer({ticketId, lastMessageTime: new Date()}))

    res.status(201).json(message);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

export const getMessages = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = 50;

    // Check conversation access
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) return res.status(404).json({ error: "Not found" });
    if (
      ![
        conversation.client.toString(),
        conversation.seller.toString(),
      ].includes(req.user.id)
    ) {
      return res.status(403).json({ error: "Unauthorized" });
    }

    const messages = await Message.find({ conversation: conversationId })
      .sort("-createdAt")
      .skip((page - 1) * limit)
      .limit(limit)
      .populate("sender", "name avatar role");

    res.json({
      messages: messages.reverse(), // Return oldest first for proper display
      hasMore: messages.length === limit,
    });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

export const markAsRead = async (req, res) => {
  try {
    const { conversationId } = req.body;
    const userId = req.user.id;

    // Update messages
    await Message.updateMany(
      {
        conversation: conversationId,
        sender: { $ne: userId },
        read: false,
      },
      { $set: { read: true } }
    );

    // Update conversation unread count
    const conversation = await Conversation.findById(conversationId);
    if (userId === conversation.client.toString()) {
      conversation.unreadClient = 0;
    } else {
      conversation.unreadSeller = 0;
    }
    await conversation.save();

    res.json({ success: true });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};
