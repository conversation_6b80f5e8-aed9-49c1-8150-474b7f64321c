import Ticket from "./../models/ticketModel.js";
import Message from "./../models/messageModel.js";
import Conversation from "./../models/conversationModel.js";
import User from "./../models/userModel.js";
import * as factory from "./handlerFactory.js";

// Create a new ticket
export const createTicket = async (req, res, next) => {
  try {
    const ticketData = req.body;
    if (req.user && req.user._id) {
      ticketData.user = req.user._id;
    }
    const ticket = await Ticket.create(ticketData);

    // Create conversation for the ticket
    try {
      const admin = await User.findOne({ role: 'admin' });
      if (!admin) {
        console.error('No admin user found for conversation creation');
      } else {
        const conversation = new Conversation({
          offer: null,
          client: ticket.user || null, // For guest tickets, client will be null
          seller: admin._id,
          type: 'ticket'
        });

        await conversation.save();

        // Update ticket with conversation ID
        ticket.conversationId = conversation._id;
        await ticket.save();

        console.log('Conversation created for ticket:', ticket._id);
      }
    } catch (convErr) {
      console.error('Error creating conversation for ticket:', convErr);
    }

    res.status(201).json({ status: 'success', data: ticket });
  } catch (err) {
    next(err);
  }
};

// Get all tickets (admin only or ticket owner)
export const getAllTickets = async (req, res, next) => {
  try {

    let filter = {};
    // If user is not admin, filter to show only their own tickets
    if (req.user && req.user.role !== 'admin') {
      filter.user = req.user._id;
    }
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const skip = (page - 1) * limit;
    const total = await Ticket.countDocuments(filter);

    const tickets = await Ticket.find(filter)
      .populate('user', 'name email firstName lastName avatar role')
      .populate('conversationId')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    console.log('Raw tickets from DB:', tickets.length);
    if (tickets.length > 0) {
      console.log('Sample ticket structure:');
      console.log('  ID:', tickets[0]._id);
      console.log('  Subject:', tickets[0].subject);
      console.log('  ConversationId:', tickets[0].conversationId);
      console.log('  ConversationId type:', typeof tickets[0].conversationId);
      console.log('  Has conversationId:', !!tickets[0].conversationId);
    }

    const pages = Math.ceil(total / limit);
    res.status(200).json({
      status: 'success',
      results: tickets.length,
      pagination: {
        currentPage: page,
        pages,
        total,
        hasNextPage: page < pages,
        hasPrevPage: page > 1
      },
      data: tickets,
    });
    console.log('get all tickets:', tickets.length);
  } catch (err) {
    next(err);
  }
};

// Get a ticket by ID with enhanced details
export const getTicketById = async (req, res, next) => {
  try {
    const ticketId = req.params.id;
    const userId = req.user._id;
    const userRole = req.user.role;

    // Build query with population
    let query = Ticket.findById(ticketId)
      .populate('user', 'firstName lastName email avatar role')
      .populate({
        path: 'conversationId',
        populate: [
          {
            path: 'client',
            select: 'firstName lastName email avatar role'
          },
          {
            path: 'seller',
            select: 'firstName lastName email avatar role'
          }
        ]
      });

    const ticket = await query;
   console.log('get ticket id:', ticket);
    if (!ticket) {
      return res.status(404).json({
        status: 'fail',
        message: 'No ticket found with that ID'
      });
    }

    // Check authorization - user can only see their own tickets unless admin
    if (userRole !== 'admin' && ticket.user && ticket.user._id.toString() !== userId.toString()) {
      return res.status(403).json({
        status: 'fail',
        message: 'You can only access your own tickets'
      });
    }

    // For guest tickets, check if email matches (if provided in query)
    if (!ticket.user && userRole !== 'admin') {
      const emailQuery = req.query.email;
      if (!emailQuery || ticket.email !== emailQuery.toLowerCase()) {
        return res.status(403).json({
          status: 'fail',
          message: 'Access denied'
        });
      }
    }

    // Get messages for the conversation if it exists
    let ticketWithMessages = ticket.toObject();
    if (ticketWithMessages.conversationId && ticketWithMessages.conversationId._id) {
      try {
        const messages = await Message.find({
          conversation: ticketWithMessages.conversationId._id
        })
        .populate('sender', 'name firstName lastName email avatar role')
        .sort({ createdAt: 1 }); // Oldest first for proper conversation flow

        ticketWithMessages.conversationId.messages = messages;
      } catch (messageErr) {
        console.error('Error fetching messages for conversation:', messageErr);
        ticketWithMessages.conversationId.messages = [];
      }
    }

    res.status(200).json({
      status: 'success',
      data: ticketWithMessages
    });

  } catch (err) {
    next(err);
  }
};

// Get user's tickets with enhanced filtering and pagination
export const getUserTickets = async (req, res, next) => {
  try {
    const userId = req.user._id;
    const {
      page = 1,
      limit = 10,
      status,
      inquiryType,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Parse pagination
    const parsedPage = Math.max(1, parseInt(page));
    const parsedLimit = Math.max(1, Math.min(50, parseInt(limit))); // Max 50 items per page
    const skip = (parsedPage - 1) * parsedLimit;

    // Build filter
    const filter = { user: userId };

    if (status) {
      filter.status = status;
    }

    if (inquiryType) {
      filter.inquiryType = inquiryType;
    }

    // Build search query
    if (search) {
      filter.$or = [
        { subject: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { ticketNumber: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Get total count for pagination
    const total = await Ticket.countDocuments(filter);

    // Get tickets with population
    const tickets = await Ticket.find(filter)
      .populate('user', 'firstName lastName email avatar')
      .populate({
        path: 'conversationId',
        select: 'lastMessage unreadClient unreadSeller client seller type',
        populate: [
          {
            path: 'client',
            select: 'firstName lastName email avatar role name'
          },
          {
            path: 'seller',
            select: 'firstName lastName email avatar role name'
          }
        ]
      })
      .sort(sort)
      .skip(skip)
      .limit(parsedLimit);

    // Get messages for each ticket's conversation
    const ticketsWithMessages = await Promise.all(
      tickets.map(async (ticket) => {
        const ticketObj = ticket.toObject();

        if (ticketObj.conversationId && ticketObj.conversationId._id) {
          try {
            // Get messages for this conversation
            const messages = await Message.find({
              conversation: ticketObj.conversationId._id
            })
            .populate('sender', 'name firstName lastName email avatar role')
            .sort({ createdAt: 1 }) // Oldest first for proper conversation flow
            .limit(50); // Limit to last 50 messages for performance

            ticketObj.conversationId.messages = messages;
          } catch (messageErr) {
            console.error('Error fetching messages for conversation:', messageErr);
            ticketObj.conversationId.messages = [];
          }
        }

        return ticketObj;
      })
    );

    // Calculate pagination info
    const totalPages = Math.ceil(total / parsedLimit);

    res.status(200).json({
      status: 'success',
      results: ticketsWithMessages.length,
      pagination: {
        currentPage: parsedPage,
        totalPages,
        total,
        hasNextPage: parsedPage < totalPages,
        hasPrevPage: parsedPage > 1,
        limit: parsedLimit
      },
      data: ticketsWithMessages
    });

  } catch (err) {
    next(err);
  }
};

// Post a message to a ticket conversation
export const postTicketMessage = async (req, res, next) => {
  try {
    const { ticketId } = req.params;
    const { content, attachments } = req.body;
    const userId = req.user._id;
    const userRole = req.user.role;

    if (!content || content.trim().length === 0) {
      return res.status(400).json({
        status: 'fail',
        message: 'Message content is required'
      });
    }

    // First, get the ticket and verify access
    const ticket = await Ticket.findById(ticketId).populate('conversationId');

    if (!ticket) {
      return res.status(404).json({
        status: 'fail',
        message: 'Ticket not found'
      });
    }

    // Check authorization - user can only message their own tickets unless admin
    if (userRole !== 'admin' && ticket.user && ticket.user.toString() !== userId.toString()) {
      return res.status(403).json({
        status: 'fail',
        message: 'You can only message your own tickets'
      });
    }

    // For guest tickets, check if email matches (if provided in query)
    if (!ticket.user && userRole !== 'admin') {
      const emailQuery = req.query.email;
      if (!emailQuery || ticket.email !== emailQuery.toLowerCase()) {
        return res.status(403).json({
          status: 'fail',
          message: 'Access denied'
        });
      }
    }

    if (!ticket.conversationId) {
      return res.status(404).json({
        status: 'fail',
        message: 'No conversation found for this ticket'
      });
    }

    // Create the message
    const message = new Message({
      conversation: ticket.conversationId._id,
      sender: userId,
      content: content.trim(),
      attachments: attachments || []
    });

    await message.save();

    // Populate sender information
    await message.populate('sender', 'name firstName lastName email avatar role');

    // Update conversation's last message and unread counts
    const conversation = ticket.conversationId;
    conversation.lastMessage = content.trim();

    // Update unread count for the receiver
    if (userId.toString() === conversation.client.toString()) {
      conversation.unreadSeller += 1;
    } else {
      conversation.unreadClient += 1;
    }

    await conversation.save();

    // Update ticket status to 'responded' if it was 'open'
    if (ticket.status === 'open') {
      ticket.status = 'responded';
      await ticket.save();
    }

    res.status(201).json({
      status: 'success',
      data: {
        message,
        ticket: {
          _id: ticket._id,
          ticketNumber: ticket.ticketNumber,
          subject: ticket.subject,
          status: ticket.status
        }
      }
    });

  } catch (err) {
    next(err);
  }
};



// Get messages for a specific ticket conversation
export const getTicketMessages = async (req, res, next) => {
  try {
    const { ticketId } = req.params;
    const userId = req.user._id;
    const userRole = req.user.role;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;

    // First, get the ticket and verify access
    const ticket = await Ticket.findById(ticketId).populate('conversationId');

    if (!ticket) {
      return res.status(404).json({
        status: 'fail',
        message: 'Ticket not found'
      });
    }

    // Check authorization - user can only see their own tickets unless admin
    if (userRole !== 'admin' && ticket.user && ticket.user.toString() !== userId.toString()) {
      return res.status(403).json({
        status: 'fail',
        message: 'You can only access your own ticket conversations'
      });
    }

    // For guest tickets, check if email matches (if provided in query)
    if (!ticket.user && userRole !== 'admin') {
      const emailQuery = req.query.email;
      if (!emailQuery || ticket.email !== emailQuery.toLowerCase()) {
        return res.status(403).json({
          status: 'fail',
          message: 'Access denied'
        });
      }
    }

    if (!ticket.conversationId) {
      return res.status(404).json({
        status: 'fail',
        message: 'No conversation found for this ticket'
      });
    }

    // Get messages for the conversation
    const messages = await Message.find({
      conversation: ticket.conversationId._id
    })
    .populate('sender', 'name firstName lastName email avatar role')
    .sort({ createdAt: -1 }) // Newest first for pagination
    .skip(skip)
    .limit(limit);

    // Get total count for pagination
    const total = await Message.countDocuments({
      conversation: ticket.conversationId._id
    });

    const totalPages = Math.ceil(total / limit);

    res.status(200).json({
      status: 'success',
      results: messages.length,
      pagination: {
        currentPage: page,
        totalPages,
        total,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
        limit
      },
      data: {
        messages: messages.reverse(), // Return oldest first for proper display
        conversation: ticket.conversationId,
        ticket: {
          _id: ticket._id,
          ticketNumber: ticket.ticketNumber,
          subject: ticket.subject,
          status: ticket.status
        }
      }
    });

  } catch (err) {
    next(err);
  }
};

// Update a ticket's status
export const updateTicketStatus = factory.updateOne(Ticket);

// Delete a ticket
export const deleteTicket = factory.deleteOne(Ticket);
