import express from "express";
import {
  createTicket,
  getAllTickets,
  getTicketById,
  getUserTickets,
  updateTicketStatus,
  deleteTicket,
  getTicketMessages,
  postTicketMessage,
} from "../controllers/ticketController.js";
import { protect, admin, optionalAuth } from "../middleware/authMiddleware.js";

const router = express.Router();

// Create a new ticket (any user, with optional auth to link to user if logged in)
router.route("/").post(optionalAuth, createTicket).get(protect, getAllTickets);

// Get user's own tickets (authenticated users)
router.route("/my-tickets").get(protect, getUserTickets);

// Ticket conversation routes
router.route("/:ticketId/messages")
  .get(protect, getTicketMessages)
  .post(protect, postTicketMessage);

// Get, update status, or delete a ticket by ID (admin only for update & delete)
router
  .route("/:id")
  .get(protect, getTicketById)
  .patch(protect, updateTicketStatus)
  .delete(protect, admin, deleteTicket);

export default router;
