import mongoose from 'mongoose';
import Ticket from './models/ticketModel.js';
import Conversation from './models/conversationModel.js';
import User from './models/userModel.js';

// Simple test to check if conversationId is populated in tickets
async function testConversationPopulation() {
  try {
    // Connect to MongoDB (you'll need to set your connection string)
    // await mongoose.connect('your-mongodb-connection-string');
    
    console.log('Testing conversation population in tickets...');
    
    // Get all tickets and check if they have conversationId
    const tickets = await Ticket.find({})
      .populate('conversationId')
      .limit(5);
    
    console.log(`Found ${tickets.length} tickets`);
    
    tickets.forEach((ticket, index) => {
      console.log(`\nTicket ${index + 1}:`);
      console.log(`  ID: ${ticket._id}`);
      console.log(`  Subject: ${ticket.subject}`);
      console.log(`  ConversationId: ${ticket.conversationId ? ticket.conversationId._id : 'NULL'}`);
      console.log(`  Conversation Type: ${ticket.conversationId ? ticket.conversationId.type : 'N/A'}`);
    });
    
    // Check if there are any tickets without conversationId
    const ticketsWithoutConversation = await Ticket.find({ conversationId: { $exists: false } });
    console.log(`\nTickets without conversationId: ${ticketsWithoutConversation.length}`);
    
    if (ticketsWithoutConversation.length > 0) {
      console.log('Creating conversations for tickets without conversationId...');
      
      const admin = await User.findOne({ role: 'admin' });
      if (!admin) {
        console.log('No admin user found!');
        return;
      }
      
      for (const ticket of ticketsWithoutConversation) {
        const conversation = new Conversation({
          offer: null,
          client: ticket.user || null,
          seller: admin._id,
          type: 'ticket'
        });
        
        await conversation.save();
        ticket.conversationId = conversation._id;
        await ticket.save();
        
        console.log(`Created conversation for ticket: ${ticket._id}`);
      }
    }
    
  } catch (error) {
    console.error('Error testing conversation population:', error);
  }
}

// Export for use in other files
export { testConversationPopulation };

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testConversationPopulation();
}
